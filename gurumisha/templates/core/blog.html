{% extends 'base.html' %}
{% load static %}

{% block title %}Resources - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<!-- Enhanced Hero Section with Video Background -->
<section class="relative min-h-[70vh] flex items-center justify-center overflow-hidden">
    <!-- Video Background -->
    <div class="absolute inset-0 z-0">
        <video autoplay muted loop class="w-full h-full object-cover">
            <source src="{% static 'images/video.mp4' %}" type="video/mp4">
        </video>
        <div class="absolute inset-0 bg-gradient-to-r from-harrier-dark/90 via-harrier-dark/70 to-transparent"></div>
    </div>

    <!-- Hero Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in-up" style="animation-delay: 0.2s;">
            <h1 class="text-5xl md:text-7xl font-montserrat font-bold text-white mb-6 leading-tight">
                AUTOMOTIVE
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-harrier-red to-red-400">
                    RESOURCES
                </span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-200 mb-8 font-raleway max-w-3xl mx-auto">
                Expert guides, tips, and insights to help you make informed automotive decisions
            </p>
        </div>

        <!-- Enhanced Search Bar -->
        <div class="animate-fade-in-up max-w-2xl mx-auto" style="animation-delay: 0.4s;">
            <form class="relative" hx-get="{% url 'core:resources' %}" hx-target="#resources-content" hx-trigger="submit, keyup delay:500ms from:input[name='search']">
                <div class="relative">
                    <input type="text" name="search" placeholder="Search articles, guides, tips..."
                           class="w-full px-6 py-4 pl-14 pr-32 text-lg rounded-2xl border-0 bg-white/95 backdrop-blur-md shadow-2xl focus:outline-none focus:ring-4 focus:ring-harrier-red/30 font-raleway"
                           value="{{ request.GET.search }}">
                    <i class="fas fa-search absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
                    <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-harrier-red to-red-600 text-white px-6 py-2 rounded-xl font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg">
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Content Type Filters -->
<section class="py-12 bg-gradient-to-b from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                <button class="content-filter-btn active" data-filter="all"
                        hx-get="{% url 'core:resources' %}" hx-target="#resources-content">
                    <i class="fas fa-th-large mr-2"></i>All Content
                </button>
                <button class="content-filter-btn" data-filter="article"
                        hx-get="{% url 'core:resources' %}?content_type=article" hx-target="#resources-content">
                    <i class="fas fa-newspaper mr-2"></i>Articles
                </button>
                <button class="content-filter-btn" data-filter="guide"
                        hx-get="{% url 'core:resources' %}?content_type=guide" hx-target="#resources-content">
                    <i class="fas fa-book mr-2"></i>Guides
                </button>
                <button class="content-filter-btn" data-filter="infographic"
                        hx-get="{% url 'core:resources' %}?content_type=infographic" hx-target="#resources-content">
                    <i class="fas fa-chart-bar mr-2"></i>Infographics
                </button>
                <button class="content-filter-btn" data-filter="opinion"
                        hx-get="{% url 'core:resources' %}?content_type=opinion" hx-target="#resources-content">
                    <i class="fas fa-comment mr-2"></i>Opinions
                </button>
                <button class="content-filter-btn" data-filter="news"
                        hx-get="{% url 'core:resources' %}?content_type=news" hx-target="#resources-content">
                    <i class="fas fa-rss mr-2"></i>News
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Main Resources Content -->
<section class="py-16 bg-white" id="resources-content">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if posts %}
            <!-- Featured Content -->
            {% if featured_posts %}
            <div class="mb-16 animate-fade-in-up" style="animation-delay: 0.2s;">
                <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">Featured Content</h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {% for post in featured_posts %}
                    <article class="group relative overflow-hidden rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="relative h-80 overflow-hidden">
                            {% if post.featured_image %}
                                <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}"
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                            {% else %}
                                <div class="w-full h-full bg-gradient-to-br from-harrier-red/20 to-harrier-dark/20 flex items-center justify-center">
                                    <i class="fas fa-{{ post.content_type|default:'newspaper' }} text-harrier-red text-6xl"></i>
                                </div>
                            {% endif %}
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>

                            <!-- Content Type Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-harrier-red text-white">
                                    <i class="fas fa-{{ post.content_type|default:'newspaper' }} mr-1"></i>
                                    {{ post.content_type_display|default:'Article' }}
                                </span>
                            </div>

                            <!-- Featured Badge -->
                            <div class="absolute top-4 right-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            </div>
                        </div>

                        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                            <div class="flex items-center text-sm mb-3 opacity-90">
                                <i class="fas fa-calendar mr-2"></i>
                                <span>{{ post.published_at|date:"M d, Y" }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ post.estimated_read_time }} min read</span>
                                {% if post.views_count %}
                                <span class="mx-2">•</span>
                                <span>{{ post.views_count }} views</span>
                                {% endif %}
                            </div>

                            <h3 class="text-2xl font-montserrat font-bold mb-3 group-hover:text-harrier-red transition-colors">
                                <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                            </h3>

                            <p class="text-gray-200 mb-4 font-raleway">{{ post.excerpt|default:post.content|truncatewords:15 }}</p>

                            <a href="{% url 'core:resource_detail' post.slug %}"
                               class="inline-flex items-center text-white font-semibold hover:text-harrier-red transition-colors group">
                                Read More
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </a>
                        </div>
                    </article>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Regular Content Grid -->
            <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
                {% if not featured_posts %}
                <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">Latest Resources</h2>
                {% else %}
                <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">More Resources</h2>
                {% endif %}

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {% for post in posts %}
                        {% if not post.is_featured %}
                        <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 overflow-hidden border border-gray-100">
                            <div class="relative h-48 overflow-hidden">
                                {% if post.featured_image %}
                                    <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}"
                                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                {% else %}
                                    <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                        <i class="fas fa-{{ post.content_type|default:'newspaper' }} text-gray-400 text-4xl"></i>
                                    </div>
                                {% endif %}

                                <!-- Content Type Badge -->
                                <div class="absolute top-3 left-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold bg-white/90 text-harrier-dark">
                                        <i class="fas fa-{{ post.content_type|default:'newspaper' }} mr-1"></i>
                                        {{ post.content_type_display|default:'Article' }}
                                    </span>
                                </div>

                                {% if post.difficulty_level %}
                                <div class="absolute top-3 right-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold
                                        {% if post.difficulty_level == 'beginner' %}bg-green-100 text-green-800
                                        {% elif post.difficulty_level == 'intermediate' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ post.difficulty_level|title }}
                                    </span>
                                </div>
                                {% endif %}
                            </div>

                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 mb-3 font-raleway">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>{{ post.published_at|date:"M d, Y" }}</span>
                                    <span class="mx-2">•</span>
                                    <span>{{ post.estimated_read_time }} min read</span>
                                    {% if post.views_count %}
                                    <span class="mx-2">•</span>
                                    <span>{{ post.views_count }} views</span>
                                    {% endif %}
                                </div>

                                <h3 class="text-xl font-montserrat font-bold text-harrier-dark mb-3 group-hover:text-harrier-red transition-colors line-clamp-2">
                                    <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                                </h3>

                                <p class="text-gray-600 mb-4 font-raleway line-clamp-3">{{ post.excerpt|default:post.content|truncatewords:20 }}</p>

                                <!-- Tags -->
                                {% if post.tags.all %}
                                <div class="flex flex-wrap gap-2 mb-4">
                                    {% for tag in post.tags.all|slice:":3" %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700">
                                        {{ tag.name }}
                                    </span>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                <div class="flex items-center justify-between">
                                    <a href="{% url 'core:resource_detail' post.slug %}"
                                       class="inline-flex items-center text-harrier-red font-semibold hover:text-harrier-dark transition-colors group">
                                        Read More
                                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                                    </a>

                                    <div class="flex items-center space-x-3 text-gray-400">
                                        {% if post.likes_count %}
                                        <span class="flex items-center text-sm">
                                            <i class="fas fa-heart mr-1"></i>{{ post.likes_count }}
                                        </span>
                                        {% endif %}
                                        <button class="hover:text-harrier-red transition-colors" title="Bookmark">
                                            <i class="fas fa-bookmark"></i>
                                        </button>
                                        <button class="hover:text-harrier-red transition-colors" title="Share">
                                            <i class="fas fa-share"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </article>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>

            <!-- Enhanced Pagination -->
            {% if is_paginated %}
            <div class="flex justify-center mt-16 animate-fade-in-up" style="animation-delay: 0.4s;">
                <nav class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.content_type %}&content_type={{ request.GET.content_type }}{% endif %}"
                           class="px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-harrier-red hover:text-white hover:border-harrier-red transition-all duration-300 font-raleway">
                            <i class="fas fa-angle-double-left mr-1"></i>First
                        </a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.content_type %}&content_type={{ request.GET.content_type }}{% endif %}"
                           class="px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-harrier-red hover:text-white hover:border-harrier-red transition-all duration-300 font-raleway">
                            <i class="fas fa-angle-left mr-1"></i>Previous
                        </a>
                    {% endif %}

                    <!-- Page Numbers -->
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-4 py-2 bg-gradient-to-r from-harrier-red to-red-600 text-white rounded-xl font-semibold shadow-lg">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.content_type %}&content_type={{ request.GET.content_type }}{% endif %}"
                               class="px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-harrier-red hover:text-white hover:border-harrier-red transition-all duration-300 font-raleway">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.content_type %}&content_type={{ request.GET.content_type }}{% endif %}"
                           class="px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-harrier-red hover:text-white hover:border-harrier-red transition-all duration-300 font-raleway">
                            Next<i class="fas fa-angle-right ml-1"></i>
                        </a>
                        <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.content_type %}&content_type={{ request.GET.content_type }}{% endif %}"
                           class="px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-harrier-red hover:text-white hover:border-harrier-red transition-all duration-300 font-raleway">
                            Last<i class="fas fa-angle-double-right ml-1"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
            {% endif %}
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-24 animate-fade-in-up">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-gradient-to-br from-harrier-red/20 to-harrier-dark/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-newspaper text-harrier-red text-4xl"></i>
                    </div>
                    <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-4">No Content Found</h3>
                    <p class="text-gray-600 font-raleway mb-6">
                        {% if request.GET.search %}
                            No results found for "{{ request.GET.search }}". Try different keywords or browse all content.
                        {% elif request.GET.content_type %}
                            No {{ request.GET.content_type }} content available yet. Check back soon for updates.
                        {% else %}
                            Check back soon for automotive news, guides, and insights.
                        {% endif %}
                    </p>
                    <a href="{% url 'core:resources' %}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-harrier-red to-red-600 text-white rounded-xl font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg">
                        <i class="fas fa-arrow-left mr-2"></i>View All Content
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</section>

<!-- Enhanced Newsletter Signup -->
<section class="py-20 bg-gradient-to-br from-harrier-dark via-harrier-dark to-black relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>

    <div class="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in-up" style="animation-delay: 0.1s;">
            <h2 class="text-4xl md:text-5xl font-montserrat font-bold text-white mb-6">
                Stay <span class="text-transparent bg-clip-text bg-gradient-to-r from-harrier-red to-red-400">Updated</span>
            </h2>
            <p class="text-xl text-gray-300 mb-10 font-raleway max-w-2xl mx-auto">
                Get the latest automotive news, expert guides, and exclusive insights delivered straight to your inbox.
            </p>
        </div>

        <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
            <form class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto mb-8" id="newsletter-form">
                <div class="flex-1 relative">
                    <input type="email" placeholder="Enter your email address"
                           class="w-full px-6 py-4 rounded-xl text-gray-900 bg-white/95 backdrop-blur-md border-0 focus:outline-none focus:ring-4 focus:ring-harrier-red/30 font-raleway shadow-lg" required>
                    <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <button type="submit" class="bg-gradient-to-r from-harrier-red to-red-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg transform hover:scale-105">
                    <i class="fas fa-paper-plane mr-2"></i>Subscribe
                </button>
            </form>

            <div class="flex items-center justify-center space-x-8 text-gray-400 text-sm font-raleway">
                <span class="flex items-center">
                    <i class="fas fa-shield-alt mr-2 text-green-400"></i>Privacy Protected
                </span>
                <span class="flex items-center">
                    <i class="fas fa-times-circle mr-2 text-red-400"></i>Unsubscribe Anytime
                </span>
                <span class="flex items-center">
                    <i class="fas fa-clock mr-2 text-blue-400"></i>Weekly Updates
                </span>
            </div>
        </div>
    </div>
</section>

<!-- Custom Styles -->
<style>
/* Content Filter Buttons */
.content-filter-btn {
    @apply px-6 py-3 rounded-xl font-semibold transition-all duration-300 border-2 border-gray-200 bg-white text-gray-700 hover:border-harrier-red hover:text-harrier-red transform hover:scale-105 shadow-md;
    font-family: 'Montserrat', sans-serif;
}

.content-filter-btn.active {
    @apply bg-gradient-to-r from-harrier-red to-red-600 text-white border-harrier-red shadow-lg;
}

.content-filter-btn:hover {
    @apply shadow-xl;
}

/* Animation Classes */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
}

/* Line Clamp Utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced Hover Effects */
.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
    transform: translateX(0.25rem);
}

/* Glassmorphism Effects */
.backdrop-blur-md {
    backdrop-filter: blur(12px);
}

/* Custom Shadows */
.shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .content-filter-btn {
        @apply px-4 py-2 text-sm;
    }

    .animate-fade-in-up {
        animation-delay: 0s !important;
    }
}

/* HTMX Loading States */
.htmx-request .htmx-indicator {
    display: inline;
}

.htmx-indicator {
    display: none;
}

/* Search Input Focus Effects */
input[type="text"]:focus,
input[type="email"]:focus {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Content Filter Functionality
    const filterButtons = document.querySelectorAll('.content-filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
        });
    });

    // Newsletter Form Submission
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;

            // Show success message (you can integrate with your backend)
            alert('Thank you for subscribing! You will receive our latest updates.');
            this.reset();
        });
    }

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
{% endblock %}
