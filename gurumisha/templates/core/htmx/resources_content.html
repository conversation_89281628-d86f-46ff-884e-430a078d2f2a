<!-- HTMX Resources Content Template -->
{% load static %}

{% if posts %}
    <!-- Featured Content -->
    {% if featured_posts %}
    <div class="mb-16 animate-fade-in-up" style="animation-delay: 0.2s;">
        <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">Featured Content</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {% for post in featured_posts %}
            <article class="group relative overflow-hidden rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2">
                <div class="relative h-80 overflow-hidden">
                    {% if post.featured_image %}
                        <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}" 
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                    {% else %}
                        <div class="w-full h-full bg-gradient-to-br from-harrier-red/20 to-harrier-dark/20 flex items-center justify-center">
                            <i class="fas fa-{{ post.content_type|default:'newspaper' }} text-harrier-red text-6xl"></i>
                        </div>
                    {% endif %}
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                    
                    <!-- Content Type Badge -->
                    <div class="absolute top-4 left-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-harrier-red text-white">
                            <i class="fas fa-{{ post.content_type|default:'newspaper' }} mr-1"></i>
                            {{ post.content_type_display|default:'Article' }}
                        </span>
                    </div>
                    
                    <!-- Featured Badge -->
                    <div class="absolute top-4 right-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                            <i class="fas fa-star mr-1"></i>Featured
                        </span>
                    </div>
                </div>
                
                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <div class="flex items-center text-sm mb-3 opacity-90">
                        <i class="fas fa-calendar mr-2"></i>
                        <span>{{ post.published_at|date:"M d, Y" }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ post.estimated_read_time }} min read</span>
                        {% if post.views_count %}
                        <span class="mx-2">•</span>
                        <span>{{ post.views_count }} views</span>
                        {% endif %}
                    </div>
                    
                    <h3 class="text-2xl font-montserrat font-bold mb-3 group-hover:text-harrier-red transition-colors">
                        <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                    </h3>
                    
                    <p class="text-gray-200 mb-4 font-raleway">{{ post.excerpt|default:post.content|truncatewords:15 }}</p>
                    
                    <a href="{% url 'core:resource_detail' post.slug %}" 
                       class="inline-flex items-center text-white font-semibold hover:text-harrier-red transition-colors group">
                        Read More 
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </a>
                </div>
            </article>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Regular Content Grid -->
    <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
        {% if not featured_posts %}
        <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">
            {% if search_query %}
                Search Results for "{{ search_query }}"
            {% elif current_category %}
                {{ category_name|default:'Category' }} Content
            {% elif current_tag %}
                {{ tag_name|default:'Tag' }} Content
            {% elif current_content_type %}
                {{ current_content_type|title }} Content
            {% else %}
                Latest Resources
            {% endif %}
        </h2>
        {% else %}
        <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">More Resources</h2>
        {% endif %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for post in posts %}
                {% if not post.is_featured %}
                <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 overflow-hidden border border-gray-100">
                    <div class="relative h-48 overflow-hidden">
                        {% if post.featured_image %}
                            <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        {% else %}
                            <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                <i class="fas fa-{{ post.content_type|default:'newspaper' }} text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}
                        
                        <!-- Content Type Badge -->
                        <div class="absolute top-3 left-3">
                            <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold bg-white/90 text-harrier-dark">
                                <i class="fas fa-{{ post.content_type|default:'newspaper' }} mr-1"></i>
                                {{ post.content_type_display|default:'Article' }}
                            </span>
                        </div>
                        
                        {% if post.difficulty_level %}
                        <div class="absolute top-3 right-3">
                            <span class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold 
                                {% if post.difficulty_level == 'beginner' %}bg-green-100 text-green-800
                                {% elif post.difficulty_level == 'intermediate' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ post.difficulty_level|title }}
                            </span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 mb-3 font-raleway">
                            <i class="fas fa-calendar mr-2"></i>
                            <span>{{ post.published_at|date:"M d, Y" }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ post.estimated_read_time }} min read</span>
                            {% if post.views_count %}
                            <span class="mx-2">•</span>
                            <span>{{ post.views_count }} views</span>
                            {% endif %}
                        </div>
                        
                        <h3 class="text-xl font-montserrat font-bold text-harrier-dark mb-3 group-hover:text-harrier-red transition-colors line-clamp-2">
                            <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                        </h3>
                        
                        <p class="text-gray-600 mb-4 font-raleway line-clamp-3">{{ post.excerpt|default:post.content|truncatewords:20 }}</p>
                        
                        <!-- Tags -->
                        {% if post.tags.all %}
                        <div class="flex flex-wrap gap-2 mb-4">
                            {% for tag in post.tags.all|slice:":3" %}
                            <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700">
                                {{ tag.name }}
                            </span>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="flex items-center justify-between">
                            <a href="{% url 'core:resource_detail' post.slug %}" 
                               class="inline-flex items-center text-harrier-red font-semibold hover:text-harrier-dark transition-colors group">
                                Read More 
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                            </a>
                            
                            <div class="flex items-center space-x-3 text-gray-400">
                                {% if post.likes_count %}
                                <span class="flex items-center text-sm">
                                    <i class="fas fa-heart mr-1"></i>{{ post.likes_count }}
                                </span>
                                {% endif %}
                                <button class="hover:text-harrier-red transition-colors" title="Bookmark"
                                        hx-post="{% url 'core:content_bookmark_toggle' post.id %}"
                                        hx-swap="none">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                                <button class="hover:text-harrier-red transition-colors" title="Share">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </article>
                {% endif %}
            {% endfor %}
        </div>
    </div>
{% else %}
    <!-- Empty State -->
    <div class="text-center py-24 animate-fade-in-up">
        <div class="max-w-md mx-auto">
            <div class="w-24 h-24 bg-gradient-to-br from-harrier-red/20 to-harrier-dark/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-newspaper text-harrier-red text-4xl"></i>
            </div>
            <h3 class="text-2xl font-montserrat font-bold text-harrier-dark mb-4">No Content Found</h3>
            <p class="text-gray-600 font-raleway mb-6">
                {% if search_query %}
                    No results found for "{{ search_query }}". Try different keywords or browse all content.
                {% elif current_content_type %}
                    No {{ current_content_type }} content available yet. Check back soon for updates.
                {% elif current_category %}
                    No content in this category yet. Check back soon for updates.
                {% elif current_tag %}
                    No content with this tag yet. Check back soon for updates.
                {% else %}
                    Check back soon for automotive news, guides, and insights.
                {% endif %}
            </p>
            <a href="{% url 'core:resources' %}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-harrier-red to-red-600 text-white rounded-xl font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg">
                <i class="fas fa-arrow-left mr-2"></i>View All Content
            </a>
        </div>
    </div>
{% endif %}
