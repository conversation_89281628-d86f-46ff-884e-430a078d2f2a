{% load static %}

<div class="glassmorphism-card p-6">
    <!-- Enhanced Search and Filter Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input type="text" 
                       id="resource-search" 
                       placeholder="Search content..." 
                       value="{{ search_query }}"
                       class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm"
                       hx-get="{% url 'core:admin_resource_search' %}"
                       hx-target="#resource-tab-content"
                       hx-trigger="keyup changed delay:300ms"
                       hx-include="[name='content_type'], [name='status'], [name='category']"
                       hx-vals='{"tab": "all"}'>
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap gap-3">
            <!-- Content Type Filter -->
            <select name="content_type" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_all_content_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='status'], [name='category']">
                <option value="">All Types</option>
                {% for value, label in content_types %}
                    <option value="{{ value }}" {% if current_content_type == value %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>

            <!-- Status Filter -->
            <select name="status" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_all_content_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='content_type'], [name='category']">
                <option value="">All Status</option>
                <option value="published" {% if current_status == 'published' %}selected{% endif %}>Published</option>
                <option value="draft" {% if current_status == 'draft' %}selected{% endif %}>Draft</option>
                <option value="featured" {% if current_status == 'featured' %}selected{% endif %}>Featured</option>
            </select>

            <!-- Category Filter -->
            <select name="category" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_resource_all_content_tab' %}"
                    hx-target="#resource-tab-content"
                    hx-include="[name='search'], [name='content_type'], [name='status']">
                <option value="">All Categories</option>
                {% for category in categories %}
                    <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
            </select>

            <!-- Refresh Button -->
            <button class="enhanced-btn enhanced-btn-secondary text-sm"
                    hx-get="{% url 'core:admin_resource_all_content_tab' %}"
                    hx-target="#resource-tab-content">
                <i class="fas fa-sync-alt mr-2"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Content Table -->
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead>
                <tr class="border-b border-gray-200">
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">
                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                    </th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Title</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Type</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Author</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Status</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Views</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Updated</th>
                    <th class="text-left py-4 px-4 font-semibold text-harrier-dark">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for post in posts %}
                <tr class="border-b border-gray-100 hover:bg-gray-50/50 transition-colors">
                    <td class="py-4 px-4">
                        <input type="checkbox" class="content-checkbox rounded border-gray-300 text-harrier-red focus:ring-harrier-red" value="{{ post.id }}">
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-start space-x-3">
                            {% if post.featured_image %}
                                <img src="{{ post.featured_image.url }}" alt="{{ post.title }}" class="w-12 h-12 rounded-lg object-cover">
                            {% else %}
                                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-file-alt text-gray-400"></i>
                                </div>
                            {% endif %}
                            <div class="flex-1 min-w-0">
                                <h3 class="font-medium text-harrier-dark line-clamp-2">{{ post.title }}</h3>
                                {% if post.excerpt %}
                                    <p class="text-sm text-gray-500 line-clamp-1 mt-1">{{ post.excerpt }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <span class="content-type-badge {{ post.content_type }}">
                            {{ post.get_content_type_display }}
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center space-x-2">
                            {% if post.author.profile_picture %}
                                <img src="{{ post.author.profile_picture.url }}" alt="{{ post.author.username }}" class="w-8 h-8 rounded-full">
                            {% else %}
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-gray-400 text-xs"></i>
                                </div>
                            {% endif %}
                            <span class="text-sm text-gray-700">{{ post.author.username }}</span>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex flex-col space-y-1">
                            {% if post.is_published %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Published
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-edit mr-1"></i>Draft
                                </span>
                            {% endif %}
                            {% if post.is_featured %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            {% endif %}
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <span class="text-sm text-gray-700">{{ post.views_count|default:0 }}</span>
                    </td>
                    <td class="py-4 px-4">
                        <span class="text-sm text-gray-500">{{ post.updated_at|date:"M d, Y" }}</span>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center space-x-2">
                            <!-- Edit Button -->
                            <button class="enhanced-btn enhanced-btn-secondary text-xs px-3 py-1"
                                    hx-get="{% url 'core:admin_resource_edit_modal' post.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>

                            <!-- Toggle Published -->
                            <button class="enhanced-btn {% if post.is_published %}enhanced-btn-warning{% else %}enhanced-btn-success{% endif %} text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_toggle_published' post.id %}"
                                    hx-confirm="Are you sure you want to {% if post.is_published %}unpublish{% else %}publish{% endif %} this content?"
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="{% if post.is_published %}Unpublish{% else %}Publish{% endif %}">
                                <i class="fas fa-{% if post.is_published %}eye-slash{% else %}eye{% endif %}"></i>
                            </button>

                            <!-- Toggle Featured -->
                            <button class="enhanced-btn {% if post.is_featured %}enhanced-btn-warning{% else %}enhanced-btn-primary{% endif %} text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_toggle_featured' post.id %}"
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="{% if post.is_featured %}Remove from Featured{% else %}Add to Featured{% endif %}">
                                <i class="fas fa-star"></i>
                            </button>

                            <!-- Delete Button -->
                            <button class="enhanced-btn enhanced-btn-danger text-xs px-3 py-1"
                                    hx-post="{% url 'core:admin_resource_delete' post.id %}"
                                    hx-confirm="Are you sure you want to delete this content? This action cannot be undone."
                                    hx-target="#resource-tab-content"
                                    hx-swap="outerHTML"
                                    title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-file-alt text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-500 mb-2">No content found</h3>
                            <p class="text-gray-400">{% if search_query %}No content matches your search criteria.{% else %}Start by creating your first piece of content.{% endif %}</p>
                            {% if not search_query %}
                            <button class="enhanced-btn enhanced-btn-primary mt-4"
                                    hx-get="{% url 'core:admin_resource_create_modal' %}"
                                    hx-target="body"
                                    hx-swap="beforeend">
                                <i class="fas fa-plus mr-2"></i>Create Content
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Enhanced Pagination -->
    {% if posts.has_other_pages %}
    <div class="enhanced-pagination-container mt-6">
        <div class="pagination-content">
            <div class="pagination-info">
                <div class="info-text">
                    <span class="showing-text">Showing</span>
                    <span class="range-numbers">{{ posts.start_index }} - {{ posts.end_index }}</span>
                    <span class="of-text">of</span>
                    <span class="total-number">{{ posts.paginator.count }}</span>
                    <span class="items-text">content items</span>
                </div>
                <div class="pagination-stats">
                    <span class="page-indicator">Page {{ posts.number }} of {{ posts.paginator.num_pages }}</span>
                </div>
            </div>

            <div class="pagination-controls">
                {% if posts.has_previous %}
                    <a href="?page={{ posts.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_content_type %}&content_type={{ current_content_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_all_content_tab' %}?page={{ posts.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_content_type %}&content_type={{ current_content_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        <i class="fas fa-chevron-left mr-1"></i>
                        Previous
                    </a>
                {% endif %}

                <!-- Page numbers -->
                {% for num in posts.paginator.page_range %}
                    {% if num == posts.number %}
                        <span class="modern-pagination-btn active">{{ num }}</span>
                    {% elif num > posts.number|add:'-3' and num < posts.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_content_type %}&content_type={{ current_content_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           class="modern-pagination-btn"
                           hx-get="{% url 'core:admin_resource_all_content_tab' %}?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_content_type %}&content_type={{ current_content_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                           hx-target="#resource-tab-content">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if posts.has_next %}
                    <a href="?page={{ posts.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_content_type %}&content_type={{ current_content_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       class="modern-pagination-btn"
                       hx-get="{% url 'core:admin_resource_all_content_tab' %}?page={{ posts.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_content_type %}&content_type={{ current_content_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}"
                       hx-target="#resource-tab-content">
                        Next
                        <i class="fas fa-chevron-right ml-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.content-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Individual checkbox handling
document.querySelectorAll('.content-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const selectAll = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.content-checkbox');
        const checkedBoxes = document.querySelectorAll('.content-checkbox:checked');
        
        selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
        selectAll.checked = checkedBoxes.length === checkboxes.length;
    });
});
</script>
