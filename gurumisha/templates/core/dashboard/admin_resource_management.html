{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block title %}Resource Management - Admin Dashboard{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Enhanced Resource Management Styles */
    .resource-tab {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 140px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
        background: rgba(255, 255, 255, 0.8);
        color: var(--harrier-dark);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .resource-tab:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.95);
        border-color: var(--harrier-red);
    }

    .resource-tab.active {
        background: linear-gradient(135deg,
            var(--harrier-red) 0%,
            #dc2626 15%,
            var(--harrier-red) 35%,
            #b91c1c 65%,
            var(--harrier-red) 85%,
            #dc2626 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(185, 28, 28, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        animation: gradientShift 3s ease-in-out infinite;
    }

    .resource-tab.active:hover {
        background-position: 100% 50%;
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 12px 32px rgba(220, 38, 38, 0.5),
            0 6px 16px rgba(185, 28, 28, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    }

    @keyframes gradientShift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    .resource-stats-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .resource-stats-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        border-color: var(--harrier-red);
    }

    .resource-overview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .content-type-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .content-type-badge.article {
        background: rgba(59, 130, 246, 0.1);
        color: #1d4ed8;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .content-type-badge.guide {
        background: rgba(16, 185, 129, 0.1);
        color: #047857;
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .content-type-badge.infographic {
        background: rgba(245, 158, 11, 0.1);
        color: #92400e;
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .content-type-badge.opinion {
        background: rgba(139, 92, 246, 0.1);
        color: #6b21a8;
        border: 1px solid rgba(139, 92, 246, 0.2);
    }

    .content-type-badge.news {
        background: rgba(239, 68, 68, 0.1);
        color: #b91c1c;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .recent-content-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        transition: all 0.2s ease;
        border: 1px solid rgba(229, 231, 235, 0.5);
    }

    .recent-content-item:hover {
        background: rgba(249, 250, 251, 0.8);
        border-color: var(--harrier-red);
        transform: translateX(4px);
    }

    .performance-metric {
        text-align: center;
        padding: 1rem;
        border-radius: 12px;
        background: rgba(249, 250, 251, 0.8);
        border: 1px solid rgba(229, 231, 235, 0.5);
    }

    .performance-metric .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--harrier-red);
        font-family: 'Montserrat', sans-serif;
    }

    .performance-metric .metric-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mr-3"></i>
            <span class="text-sm font-medium text-gray-500">Resource Management</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Enhanced Header Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 animate-fade-in-up">
        <div>
            <h1 class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">Resource Management</h1>
            <p class="text-gray-600 font-raleway">Manage articles, guides, infographics, opinions, and news content</p>
        </div>

        <div class="flex flex-wrap gap-3">
            <button class="enhanced-btn enhanced-btn-secondary text-sm"
                    onclick="exportResources()">
                <i class="fas fa-download mr-2"></i>
                <span>Export Data</span>
            </button>
            <button class="enhanced-btn enhanced-btn-primary text-sm"
                    hx-get="{% url 'core:admin_resource_create_modal' %}"
                    hx-target="body"
                    hx-swap="beforeend">
                <i class="fas fa-plus mr-2"></i>
                <span>Create Content</span>
            </button>
        </div>
    </div>

    <!-- Resource Overview Statistics -->
    <div class="resource-overview-grid animate-fade-in-up" style="animation-delay: 0.1s;">
        <div class="resource-stats-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Total Content</h3>
                <div class="w-12 h-12 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center">
                    <i class="fas fa-file-alt text-harrier-red text-xl"></i>
                </div>
            </div>
            <div class="performance-metric">
                <div class="metric-value">{{ total_content }}</div>
                <div class="metric-label">Total Items</div>
            </div>
        </div>

        <div class="resource-stats-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Published Content</h3>
                <div class="w-12 h-12 bg-green-500 bg-opacity-10 rounded-full flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-500 text-xl"></i>
                </div>
            </div>
            <div class="performance-metric">
                <div class="metric-value">{{ published_content }}</div>
                <div class="metric-label">Live Content</div>
            </div>
        </div>

        <div class="resource-stats-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Draft Content</h3>
                <div class="w-12 h-12 bg-yellow-500 bg-opacity-10 rounded-full flex items-center justify-center">
                    <i class="fas fa-edit text-yellow-500 text-xl"></i>
                </div>
            </div>
            <div class="performance-metric">
                <div class="metric-value">{{ draft_content }}</div>
                <div class="metric-label">In Progress</div>
            </div>
        </div>

        <div class="resource-stats-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Featured Content</h3>
                <div class="w-12 h-12 bg-purple-500 bg-opacity-10 rounded-full flex items-center justify-center">
                    <i class="fas fa-star text-purple-500 text-xl"></i>
                </div>
            </div>
            <div class="performance-metric">
                <div class="metric-value">{{ featured_content }}</div>
                <div class="metric-label">Featured Items</div>
            </div>
        </div>
    </div>

    <!-- Content Type Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="resource-stats-card">
            <h3 class="text-lg font-semibold text-harrier-dark font-montserrat mb-4">Content Types</h3>
            <div class="space-y-3">
                {% for content_type in content_types %}
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="content-type-badge {{ content_type.content_type }}">
                            {{ content_type.content_type|title }}
                        </span>
                    </div>
                    <span class="font-semibold text-harrier-dark">{{ content_type.count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="resource-stats-card">
            <h3 class="text-lg font-semibold text-harrier-dark font-montserrat mb-4">Top Performing Content</h3>
            <div class="space-y-3">
                {% for post in top_content %}
                <div class="recent-content-item">
                    <div class="flex-1">
                        <h4 class="font-medium text-harrier-dark text-sm line-clamp-1">{{ post.title }}</h4>
                        <div class="flex items-center text-xs text-gray-500 mt-1">
                            <span class="content-type-badge {{ post.content_type }}">{{ post.content_type|title }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ post.views_count }} views</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Enhanced Resource Tabs with Harrier Design -->
    <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="glassmorphism-card p-2">
            <nav class="flex flex-wrap gap-2">
                <button class="resource-tab active" data-tab="all-content"
                        hx-get="{% url 'core:admin_resource_all_content_tab' %}"
                        hx-target="#resource-tab-content"
                        hx-swap="innerHTML">
                    <i class="fas fa-th-large mr-2"></i>
                    <span>All Content</span>
                </button>
                <button class="resource-tab" data-tab="articles"
                        hx-get="{% url 'core:admin_resource_articles_tab' %}"
                        hx-target="#resource-tab-content"
                        hx-swap="innerHTML">
                    <i class="fas fa-newspaper mr-2"></i>
                    <span>Articles</span>
                </button>
                <button class="resource-tab" data-tab="guides"
                        hx-get="{% url 'core:admin_resource_guides_tab' %}"
                        hx-target="#resource-tab-content"
                        hx-swap="innerHTML">
                    <i class="fas fa-book mr-2"></i>
                    <span>Guides</span>
                </button>
                <button class="resource-tab" data-tab="infographics"
                        hx-get="{% url 'core:admin_resource_infographics_tab' %}"
                        hx-target="#resource-tab-content"
                        hx-swap="innerHTML">
                    <i class="fas fa-chart-bar mr-2"></i>
                    <span>Infographics</span>
                </button>
                <button class="resource-tab" data-tab="opinions"
                        hx-get="{% url 'core:admin_resource_opinions_tab' %}"
                        hx-target="#resource-tab-content"
                        hx-swap="innerHTML">
                    <i class="fas fa-comment-alt mr-2"></i>
                    <span>Opinions</span>
                </button>
                <button class="resource-tab" data-tab="news"
                        hx-get="{% url 'core:admin_resource_news_tab' %}"
                        hx-target="#resource-tab-content"
                        hx-swap="innerHTML">
                    <i class="fas fa-rss mr-2"></i>
                    <span>News</span>
                </button>
            </nav>
        </div>
    </div>

    <!-- Enhanced Tab Content Area -->
    <div class="mt-8">
        <div id="resource-tab-content" class="min-h-[600px]">
            <!-- Default content will be loaded via HTMX -->
            <div class="glassmorphism-card p-8 text-center">
                <div class="animate-pulse">
                    <div class="w-16 h-16 bg-harrier-red bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-spinner fa-spin text-harrier-red text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-harrier-dark font-montserrat mb-2">Loading Content...</h3>
                    <p class="text-gray-600 font-raleway">Please wait while we load the resource management interface.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// Enhanced Tab functionality with HTMX
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.resource-tab');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');
        });
    });

    // Load default tab content on page load
    const defaultTab = document.querySelector('.resource-tab.active');
    if (defaultTab) {
        // Trigger HTMX request for default tab
        htmx.trigger(defaultTab, 'click');
    }
});

// HTMX Event Handlers
document.addEventListener('htmx:beforeRequest', function(event) {
    // Show loading state
    const tabContent = document.getElementById('resource-tab-content');
    if (tabContent && event.target.classList.contains('resource-tab')) {
        tabContent.innerHTML = `
            <div class="glassmorphism-card p-8 text-center">
                <div class="animate-pulse">
                    <div class="w-16 h-16 bg-harrier-red bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-spinner fa-spin text-harrier-red text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-harrier-dark font-montserrat mb-2">Loading Content...</h3>
                    <p class="text-gray-600 font-raleway">Please wait while we load the content.</p>
                </div>
            </div>
        `;
    }
});

// Export Resources Function
function exportResources() {
    // This would implement CSV/Excel export functionality
    alert('Export functionality would be implemented here.');
}

// Enhanced error handling
document.addEventListener('htmx:responseError', function(event) {
    showToast('Failed to load content. Please try again.', 'error');
});
</script>
{% endblock %}
